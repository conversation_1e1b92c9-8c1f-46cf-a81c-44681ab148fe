"""
Claude MCP Client with HTTP Transport

This client connects to MCP servers over HTTP transport.
This provides an interactive chat interface where <PERSON> can access and use tools
from the connected MCP server.

Setup Instructions:
1. Install dependencies: pip install mcp anthropic python-dotenv
2. Set Anthropic API key: export ANTHROPIC_API_KEY=your_key_here
3. Start the MCP server with HTTP transport (e.g., uv run uvicorn main:app --host 0.0.0.0 --port 8000)
4. Run this client: python client_claude.py [server_url]

Features:
- HTTP transport with comprehensive error handling
- URL validation and connection retry logic
- Interactive chat interface with Claude
- Automatic tool discovery and usage
- Structured and unstructured tool result handling
- Progress reporting and logging support

Example Usage:
    python client_claude.py http://localhost:8000/mcp
    python client_claude.py https://your-server.com/mcp
    python client_claude.py  # Uses default http://localhost:8000/mcp
"""

from __future__ import annotations
import asyncio
import json
import sys
from typing import Optional, List, Any, Dict, cast
from contextlib import AsyncExitStack
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

from anthropic import Anthropic
from anthropic.types import MessageParam, ToolParam
from mcp.types import TextContent

from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env file


class MCPClient:
    def __init__(self):
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self.anthropic = Anthropic()

    async def connect(self, server_url: str = "http://localhost:8000/mcp"):
        """Connect to MCP Server over HTTP transport

        Args:
            server_url: HTTP URL of the MCP server (e.g., 'http://localhost:8000/mcp')
        """
        # Validate URL format
        try:
            parsed = urlparse(server_url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
            if parsed.scheme not in ("http", "https"):
                raise ValueError("URL must use http or https scheme")
        except Exception as e:
            raise ValueError(f"Invalid server URL '{server_url}': {e}")

        try:
            # Connect using streamable HTTP transport with timeout
            print("Establishing HTTP transport connection...")
            http_transport = await asyncio.wait_for(
                self.exit_stack.enter_async_context(streamablehttp_client(server_url)),
                timeout=30.0,  # 30 second timeout for connection
            )
            self.read_stream, self.write_stream, self.session_id = http_transport

            # Create MCP session
            print("Creating MCP session...")
            self.session = await self.exit_stack.enter_async_context(ClientSession(self.read_stream, self.write_stream))

            # Initialize the session with timeout
            print("Initializing session...")
            await asyncio.wait_for(
                self.session.initialize(),
                timeout=10.0,  # 10 second timeout for initialization
            )

            # List available tools
            print("Discovering available tools...")
            toolsData = await self.session.list_tools()
            tools = toolsData.tools
            print(f"\n✅ Connected to MCP server at {server_url}")
            print(f"📋 Available tools: {[tool.name for tool in tools]}")

            if self.session_id:
                print(f"🔗 Session ID: {self.session_id}")

        except asyncio.TimeoutError:
            raise RuntimeError(f"Connection timeout: MCP server at {server_url} did not respond within 30 seconds")
        except ConnectionError as e:
            raise RuntimeError(f"Connection failed: Unable to reach MCP server at {server_url}: {e}")
        except Exception as e:
            raise RuntimeError(f"Failed to connect to MCP server at {server_url}: {e}")

    async def disconnect(self):
        """
        Disconnect from MCP server and clean up resources.

        Properly closes the HTTP transport connection and cleans up
        any associated resources.
        """
        try:
            if self.exit_stack:
                print("Disconnecting from MCP server...")
                await self.exit_stack.aclose()
                print("✅ Disconnected successfully")
        except Exception as e:
            print(f"⚠️  Warning during disconnect: {e}")

    async def query(self, query: str) -> str:
        """Process query using Claude and available tools"""
        messages: List[MessageParam] = [{"role": "user", "content": query}]

        if not self.session:
            raise RuntimeError("Not connected to MCP server. Call connect() first.")

        toolData = await self.session.list_tools()
        available_tools: List[ToolParam] = []
        for tool in toolData.tools:
            tool_param: ToolParam = {
                "name": tool.name,
                "description": tool.description or "",
                "input_schema": tool.inputSchema,
            }
            available_tools.append(tool_param)

        # Initialize Claude API call
        response = self.anthropic.messages.create(
            model="claude-3-5-sonnet-20241022", max_tokens=1000, messages=messages, tools=available_tools
        )

        final_text = []

        # Process the response content
        if response.content:
            assistant_content = []

            for content in response.content:
                if content.type == "text":
                    final_text.append(content.text)
                    assistant_content.append({"type": "text", "text": content.text})
                elif content.type == "tool_use":
                    tool_name = content.name
                    # Convert the input object to a dictionary
                    # The Anthropic API returns content.input as a dict-like object
                    tool_args: Dict[str, Any] = cast(Dict[str, Any], content.input) if content.input else {}

                    result = await self.session.call_tool(tool_name, tool_args)
                    final_text.append(f"[Calling tool {tool_name} with arguments {json.dumps(tool_args)}]")

                    # Add the tool use to assistant content
                    assistant_content.append(
                        {"type": "tool_use", "id": content.id, "name": tool_name, "input": tool_args}
                    )

                    # Add assistant message with tool use
                    messages.append({"role": "assistant", "content": assistant_content})

                    # Add user message with tool result
                    # Convert MCP result content to string for Anthropic API
                    result_content = ""
                    if hasattr(result, "content") and result.content:
                        # Handle list of content blocks from MCP
                        if isinstance(result.content, list):
                            for content_block in result.content:
                                if isinstance(content_block, TextContent):
                                    result_content += content_block.text
                                else:
                                    result_content += str(content_block)
                        else:
                            result_content = str(result.content)
                    else:
                        result_content = str(result)

                    messages.append(
                        {
                            "role": "user",
                            "content": [{"type": "tool_result", "tool_use_id": content.id, "content": result_content}],
                        }
                    )

                    # Get Claude's response to the tool result
                    follow_up_response = self.anthropic.messages.create(
                        model="claude-3-5-sonnet-20241022", max_tokens=1000, messages=messages, tools=available_tools
                    )

                    # Add the follow-up response to final text
                    if follow_up_response.content:
                        for follow_up_content in follow_up_response.content:
                            if follow_up_content.type == "text":
                                final_text.append(follow_up_content.text)

        return "\n".join(final_text)

    async def chat(self):
        """
        Interactive Chat Loop with enhanced error handling.

        Provides a user-friendly interface for chatting with Claude while
        handling various error conditions gracefully.
        """
        print("\nMCP Client Started!")
        print("Type your queries or 'quit' to exit.")
        print("Claude has access to the MCP server tools and can use them to help you.\n")

        while True:
            try:
                query = input("> ").strip()
                if query.lower() in ("quit", "exit", "q"):
                    print("Goodbye!")
                    break

                if not query:
                    continue

                print("🤔 Claude is thinking...")
                response = await self.query(query)
                print("\n" + "=" * 50)
                print("🤖 Claude's response:")
                print("=" * 50)
                print(response)
                print("=" * 50 + "\n")

            except KeyboardInterrupt:
                print("\n\nChat interrupted. Type 'quit' to exit gracefully.")
                break
            except ConnectionError as e:
                print(f"\n❌ Connection error: {e}")
                print("The MCP server may have disconnected. Please restart the client.")
                break
            except Exception as e:
                print(f"\n❌ Error: {str(e)}")
                print("Please try again or type 'quit' to exit.")


# Example usage
async def main():
    """
    Example usage of the MCPClient with Claude over HTTP transport.

    This client connects to an MCP server running over HTTP (such as the one
    started by running 'python main.py' in this repository) and provides
    an interactive chat interface where Claude can use the available MCP tools.
    """
    # Default to local server if no URL provided
    if len(sys.argv) == 1:
        server_url = "http://localhost:8000/mcp"
        print(f"No server URL provided, using default: {server_url}")
    elif len(sys.argv) == 2:
        server_url = sys.argv[1]
    else:
        print("Usage: python client_claude.py [server_url]")
        print("Examples:")
        print("  python client_claude.py")
        print("  python client_claude.py http://localhost:8000/mcp")
        print("  python client_claude.py https://your-server.com/mcp")
        sys.exit(1)

    client = MCPClient()

    try:
        # Connect to your MCP server over HTTP
        print(f"Connecting to MCP server at {server_url}...")
        await client.connect(server_url=server_url)

        print("\n" + "=" * 50)
        print("🤖 Claude MCP Client with HTTP Transport")
        print("=" * 50)
        print("Connected successfully! You can now chat with Claude.")
        print("Claude has access to the MCP tools from the connected server.")
        print("Type 'quit' to exit.")
        print("=" * 50)

        await client.chat()

    except KeyboardInterrupt:
        print("\n\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        print("4. Ensure your ANTHROPIC_API_KEY environment variable is set")
    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
