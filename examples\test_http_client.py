#!/usr/bin/env python3
"""
Test script for the HTTP transport MCP client.

This script tests the basic functionality of the refactored client_claude.py
to ensure it can properly connect to an MCP server over HTTP transport.
"""

import asyncio
import sys
from pathlib import Path

# Add the examples directory to the path so we can import client_claude
sys.path.insert(0, str(Path(__file__).parent))

from client_claude import MCPClient


async def test_connection(server_url: str = "http://localhost:8000/mcp"):
    """
    Test the HTTP transport connection to an MCP server.
    
    Args:
        server_url: The URL of the MCP server to test
    """
    print(f"Testing HTTP transport connection to: {server_url}")
    print("=" * 60)
    
    client = MCPClient()
    
    try:
        # Test connection
        print("1. Testing connection...")
        await client.connect(server_url=server_url)
        print("✅ Connection successful!")
        
        # Test tool listing
        print("\n2. Testing tool listing...")
        if client.session:
            tools_data = await client.session.list_tools()
            tools = tools_data.tools
            print(f"✅ Found {len(tools)} tools:")
            for tool in tools:
                print(f"   - {tool.name}: {tool.description or 'No description'}")
        
        # Test resource listing
        print("\n3. Testing resource listing...")
        if client.session:
            try:
                resources_data = await client.session.list_resources()
                resources = resources_data.resources
                print(f"✅ Found {len(resources)} resources:")
                for resource in resources[:5]:  # Show first 5 resources
                    print(f"   - {resource.uri}")
                if len(resources) > 5:
                    print(f"   ... and {len(resources) - 5} more")
            except Exception as e:
                print(f"⚠️  Resource listing failed (this may be normal): {e}")
        
        print("\n✅ All tests passed! The HTTP transport client is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure the MCP server is running (python main.py)")
        print("2. Verify the server URL is correct")
        print("3. Check that the server supports HTTP transport")
        return False
        
    finally:
        await client.disconnect()
    
    return True


async def main():
    """Main test function."""
    if len(sys.argv) > 1:
        server_url = sys.argv[1]
    else:
        server_url = "http://localhost:8000/mcp"
    
    success = await test_connection(server_url)
    
    if success:
        print("\n🎉 HTTP transport client is ready to use!")
        print(f"You can now run: python client_claude.py {server_url}")
    else:
        print("\n❌ Tests failed. Please check the server and try again.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
