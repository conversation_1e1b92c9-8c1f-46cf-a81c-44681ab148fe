# Claude MCP Client with HTTP Transport

This document describes the refactored `client_claude.py` that now uses HTTP transport instead of stdio transport for connecting to MCP servers.

## Overview

The original `client_claude.py` was designed to connect to MCP servers running as local processes via stdio transport. The refactored version connects to MCP servers running as HTTP services, making it suitable for:

- Remote MCP servers
- Containerized deployments
- Web-based MCP services
- Production environments with better scalability

## Key Changes

### 1. Transport Layer
- **Before**: Used `stdio_client` with `StdioServerParameters`
- **After**: Uses `streamablehttp_client` with HTTP URLs

### 2. Connection Parameters
- **Before**: Accepted file paths to server scripts (`.py` or `.js`)
- **After**: Accepts HTTP URLs (e.g., `http://localhost:8000/mcp`)

### 3. Error Handling
- Added comprehensive URL validation
- Implemented connection timeouts (30s for connection, 10s for initialization)
- Enhanced error messages with troubleshooting tips
- Graceful handling of connection failures

### 4. User Experience
- Default server URL (`http://localhost:8000/mcp`) when none provided
- Better progress indicators during connection
- Enhanced chat interface with clearer formatting
- Improved error messages and troubleshooting guidance

## Setup Instructions

### Prerequisites
1. Install required dependencies:
   ```bash
   pip install mcp anthropic python-dotenv
   ```

2. Set your Anthropic API key:
   ```bash
   export ANTHROPIC_API_KEY=your_api_key_here
   ```

3. Start an MCP server with HTTP transport:
   ```bash
   python main.py  # Starts server at http://localhost:8000/mcp
   ```

### Usage

#### Basic Usage (Default Server)
```bash
python client_claude.py
```
This connects to `http://localhost:8000/mcp` by default.

#### Custom Server URL
```bash
python client_claude.py http://localhost:8000/mcp
python client_claude.py https://your-server.com/mcp
```

#### Testing the Connection
```bash
python test_http_client.py [server_url]
```

## Features

### HTTP Transport
- **Streamable HTTP**: Uses the modern streamable HTTP transport protocol
- **Session Management**: Automatic session ID handling
- **Connection Pooling**: Efficient HTTP connection management
- **Timeout Handling**: Configurable timeouts for reliability

### Error Handling
- **URL Validation**: Validates HTTP/HTTPS URLs before connection
- **Connection Timeouts**: 30-second connection timeout, 10-second initialization timeout
- **Graceful Failures**: Clear error messages with troubleshooting steps
- **Retry Logic**: Handles temporary connection issues

### User Interface
- **Interactive Chat**: Enhanced chat interface with Claude
- **Progress Indicators**: Shows connection progress and status
- **Tool Discovery**: Automatically discovers and lists available tools
- **Session Information**: Displays session ID and connection details

## Comparison with Original

| Feature | Original (stdio) | Refactored (HTTP) |
|---------|------------------|-------------------|
| Transport | stdio | HTTP/HTTPS |
| Server Type | Local process | HTTP service |
| Connection | Process spawn | HTTP request |
| Scalability | Single process | Multi-client |
| Deployment | Local only | Local/Remote |
| Error Handling | Basic | Comprehensive |
| Timeouts | None | Configurable |
| URL Validation | File path | HTTP URL |

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Ensure the MCP server is running
   - Check the server URL is correct
   - Verify network connectivity

2. **Invalid URL Error**
   - Use proper HTTP/HTTPS URLs
   - Include the full path (e.g., `/mcp`)
   - Check for typos in the URL

3. **Authentication Errors**
   - Verify ANTHROPIC_API_KEY is set
   - Check API key permissions
   - Ensure the key is valid

4. **Server Not Found**
   - Confirm the MCP server is running
   - Check the server is listening on the correct port
   - Verify the server supports HTTP transport

### Testing

Use the included test script to verify functionality:

```bash
python test_http_client.py
```

This will test:
- HTTP transport connection
- Tool discovery
- Resource listing
- Basic MCP protocol functionality

## Migration Guide

If you're migrating from the original stdio-based client:

1. **Update server startup**: Ensure your MCP server supports HTTP transport
2. **Change connection parameters**: Replace file paths with HTTP URLs
3. **Update scripts**: Modify any automation scripts to use HTTP URLs
4. **Test thoroughly**: Use the test script to verify functionality

## Security Considerations

When using HTTP transport:

1. **Use HTTPS**: For production deployments, always use HTTPS
2. **Authentication**: Implement proper authentication for remote servers
3. **Network Security**: Use appropriate firewall rules and network security
4. **API Keys**: Secure your Anthropic API key properly

## Future Enhancements

Potential improvements for future versions:

- Authentication support (OAuth, API keys)
- Connection retry logic with exponential backoff
- Configuration file support
- Multiple server connection management
- Enhanced logging and debugging options
